<template>
  <PageWrapper dense>
    <!-- 统计概览 -->
    <EventStatsOverview 
      ref="statsOverviewRef" 
      :search-params="searchParams" 
      @filter-change="handleFilterChange"
    />

    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <!-- <a-button @click="handleAdd">新增事件</a-button> -->
          <a-button
            type="primary"
            danger
            @click="multipleRemove(eventManagementRemove)"
            :disabled="!selected"
          >
            删除
          </a-button>
          <a-button
            @click="downloadExcel(eventManagementExport, '事件管理列表', getForm().getFieldsValue())"
          >
            导出
          </a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              // {
              //   label: '编辑',
              //   icon: IconEnum.EDIT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleEdit.bind(null, record),
              // },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    
    <EventManagementModal @register="registerModal" @success="handleSuccess" />
    <EventManagementInfoModal @register="registerInfoModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import { 
    eventManagementList, 
    eventManagementExport, 
    eventManagementRemove 
  } from '@/api/security/events';
  import EventStatsOverview from './EventStatsOverview.vue';
  import EventManagementModal from './Modal.vue';
  import EventManagementInfoModal from './InfoModal.vue';
  import { useModal } from '@/components/Modal';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'EventManagement' });

  const statsOverviewRef = ref();
  const searchParams = ref({});

  const [registerTable, { reload: tableReload, multipleRemove, selected, getForm, setTableData }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: '事件管理',
    showIndexColumn: false,
    api: eventManagementList,
    rowKey: 'id',
    useSearchForm: true,
    beforeFetch: (params) => {
      // 更新查询参数，触发统计组件更新
      searchParams.value = { ...params };
      return params;
    },
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'eventManagement',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        ['occurrenceTime', ['params[beginOccurrenceTime]', 'params[endOccurrenceTime]'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
        ['createTime', ['params[beginTime]', 'params[endTime]'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ],
    },
    columns: columns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  async function reload() {
    await tableReload();
    if (statsOverviewRef.value?.refresh) {
      await statsOverviewRef.value.refresh(searchParams.value);
    }
  }

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await eventManagementRemove([id]);
    await reload();
    // 刷新统计数据
    statsOverviewRef.value?.refresh();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    const { id } = record;
    openInfoModal(true, id);
  }

  function handleSuccess({ isUpdate }: { isUpdate: boolean }) {
    reload();
    // 刷新统计数据
    statsOverviewRef.value?.refresh();
  }

  // 处理统计组件的筛选事件
  function handleFilterChange(filterType: string | null) {
    if (filterType) {
      // 设置事件类型筛选
      getForm().setFieldsValue({ eventType: filterType });
    } else {
      // 清除事件类型筛选
      getForm().setFieldsValue({ eventType: undefined });
    }
    // 触发搜索
    reload();
  }
</script>

<style scoped></style>
