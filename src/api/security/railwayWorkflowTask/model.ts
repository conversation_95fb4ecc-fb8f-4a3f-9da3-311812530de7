export interface ControlTask {
  id: number; // 任务编号(Id唯一标识)
  taskNum: string; // 任务编号(ZYGK-YYYYMMDDHHmm)年月日时分秒
  constructionDayPlanNum: string; // 施工日计划编号
  stationId: string; // 站点id
  stationName: string; // 站点名称
  status: string; // 任务状态：1:执行中，2:已完成，3:异常结束
  plannedWorkingHours: string; // 计划工作时间
  powerOutageRange: string; // 停电范围
  scopeWork: string; // 作业范围
  groundLineGuardian: string; // 地线监护人
  groundLineGuardianId: string; // 地线监护人id(可多个逗号分割)
  groundLineGuardianNames?: string; // 地线监护人姓名列表(可多个逗号分割)
  stationLiaisonPerson: string; // 驻站联络人
  stationLiaisonPersonId: string; // 驻站联络人id
  workLeader: string; // 工作领导人
  workLeaderId: string; // 工作领导人id
  workTeam: string; // 工作组员
  startTime?: Date; // 实际工作开始时间
  endTime?: Date; // 实际工作结束时间
  durationMinutes?: number; // 实际工作耗时（分钟）
  remark?: string; // 备注
  createDept?: number; // 创建部门
  createBy?: number; // 创建者
  createTime?: Date; // 创建时间
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
}

// 任务相关执行人信息
export interface ArWorkflowDetailInfoVo2 {
  taskId: string; // 任务 ID
  arGlassId?: string; // AR眼镜ID
  personChargeId: string; // 执行人 ID
  personChargeName: string; // 执行人名称
  personChargeRoleType: string; // 执行人角色
}

// 流程详细信息
export interface WorkflowDetailVo {
  id: string;
  taskId: string;
  workflowId: string;
  workflowName: string; // 流程名称
  workflowInfo: string; // 流程环节内容
  orderNum: number; // 顺序号
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  durationMinutes?: number; // 持续时间（分钟）
  personChargeId: string;
  personChargeName: string;
  personChargeRoleType: string; // 执行人角色
  status: string; // 状态：1-未开始，2-进行中，3-已完成
  isMultipleAttachments?: string;
  preStepIds?: string;
  annexType: string;
  annexDescription?: string;
  detectionItem?: string;
  arGlassId?: string;
  remark?: string;
  annexUrl?: string;
  uploadAnnexType?: string;
  tip?: string;
}

// 获取流程信息的返回结构
export interface WorkflowListResponseVo {
  constructionDayPlanNum?: string; // 施工日计划编号
  workArea: string; // 工作区域
  workTicketNum: string; // 工作票号
  personName: string; // 人员姓名
  personChargeRoleType: string; // 执行人角色
  workflowList: WorkflowDetailVo[]; // 流程列表
}

// 获取流程信息的请求参数
export interface GetWorkflowListParams {
  taskId: string; // 任务 ID
  personChargeId: string; // 执行人 ID
}
