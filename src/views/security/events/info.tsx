import { DescItem } from '@/components/Description';
import { Tag } from 'ant-design-vue';
import MultiAnnexRender from './components/MultiAnnexRender.vue';
import {
  eventTypeMap,
  eventLevelMap,
  eventStatusMap,
  taskTypeMap,
  eventTypeColorMap,
  eventLevelColorMap,
  eventStatusColorMap,
} from './dict';

// 事件管理详情页面字段配置
export const descSchema: DescItem[] = [
  {
    label: '事件编号',
    field: 'id',
  },
  {
    label: '事件类型',
    field: 'eventType',
    render: (value) => {
      if (!value) return '-';
      const text = eventTypeMap[value] || value;
      const color = eventTypeColorMap[value];
      return <Tag color={color}>{text}</Tag>;
    },
  },
  {
    label: '事件级别',
    field: 'eventLevel',
    render: (value) => {
      if (!value) return '-';
      const text = eventLevelMap[value] || value;
      const color = eventLevelColorMap[value];
      return <Tag color={color}>{text}</Tag>;
    },
  },
  {
    label: '事件状态',
    field: 'status',
    render: (value) => {
      if (!value) return '-';
      const text = eventStatusMap[value] || value;
      const color = eventStatusColorMap[value];
      return <Tag color={color}>{text}</Tag>;
    },
  },
  {
    label: '发生时间',
    field: 'occurrenceTime',
    render: (value) => value || '-',
  },
  {
    label: '责任人',
    field: 'responsiblePerson',
    render: (value) => value || '-',
  },
  {
    label: '关联任务ID',
    field: 'taskId',
    render: (value) => value || '-',
  },
  {
    label: '任务类型',
    field: 'taskType',
    render: (value) => {
      if (!value) return '-';
      const text = taskTypeMap[value] || value;
      return <Tag>{text}</Tag>;
    },
  },
  {
    label: 'AR眼镜ID',
    field: 'arGlassId',
    render: (value) => value || '-',
  },
  {
    label: 'AR眼镜编号',
    field: 'arGlassNum',
    render: (value) => value || '-',
  },
  {
    label: '附件地址',
    field: 'annexUrl',
    render: (value) => {
      return <MultiAnnexRender value={value} />;
    },
  },
  // {
  //   label: '附件类型',
  //   field: 'annexType',
  //   render: (value) => value || '-',
  // },
  {
    label: '创建时间',
    field: 'createTime',
    render: (value) => value || '-',
  },
  {
    label: '更新时间',
    field: 'updateTime',
    render: (value) => value || '-',
  },
  {
    label: '备注',
    field: 'remark',
    span: 2,
    render: (value) => value || '-',
  },
];
