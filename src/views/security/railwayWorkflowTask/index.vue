<template>
  <PageWrapper dense>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="
              downloadExcel(controlTaskExport, '作业管控任务管理', getForm().getFieldsValue())
            "
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(controlTaskRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <!-- <a-button type="primary" @click="handleAdd">新增</a-button> -->
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '实时画面',
                icon: IconEnum.PLAY,
                type: 'primary',
                ghost: true,
                onClick: handleLiveStream.bind(null, record),
              },
              {
                label: '查看录播',
                icon: IconEnum.NEXT,
                type: 'primary',
                ghost: true,
                onClick: handleRecord.bind(null, record),
              },
              {
                label: '详情',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '修改',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '任务报告',
                icon: IconEnum.VIEW,
                type: 'primary',
                ghost: true,
                onClick: handleTaskReport.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ControlTaskModal @register="registerModal" @reload="reload" />
    <ControlTaskInfoModal width="80%" @register="registerInfoModal" />
    <ControlTaskRecordModal width="80%" @register="registerRecordModal" />
    <TaskReportModal width="90%" @register="registerTaskReportModal" />
    <LiveStreamModal @register="registerLiveStreamModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    controlTaskList,
    controlTaskExport,
    controlTaskRemove,
    postControlTaskGetLiveVideoUrl,
  } from '@/api/security/railwayWorkflowTask';
  import ControlTaskModal from './Modal.vue';
  import { useModal } from '@/components/Modal';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import ControlTaskInfoModal from './InfoModal.vue';
  import ControlTaskRecordModal from './RecordModal.vue';
  import TaskReportModal from './TaskReportModal.vue';
  import { LiveStreamModal } from '@/components/LiveStreamModal';

  defineOptions({ name: 'ControlTask' });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: '作业管控任务',
    showIndexColumn: false,
    api: controlTaskList,
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'controlTask',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        [
          'startTime',
          ['params[startTimeBegin]', 'params[startTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
        [
          'endTime',
          ['params[endTimeBegin]', 'params[endTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
      ],
    },
    columns: columns,
    actionColumn: {
      width: 500,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id: taskId } = record;
    await controlTaskRemove([taskId]);
    await reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    const { id: taskId } = record;
    openInfoModal(true, taskId);
  }

  const [registerRecordModal, { openModal: openRecordModal }] = useModal();

  function handleRecord(record: Recordable) {
    const { id: taskId } = record;
    openRecordModal(true, taskId);
  }

  const [registerTaskReportModal, { openModal: openTaskReportModal }] = useModal();

  function handleTaskReport(record: Recordable) {
    openTaskReportModal(true, record);
  }

  const [registerLiveStreamModal, { openModal: openLiveStreamModal }] = useModal();

  async function handleLiveStream(record: Recordable) {
    const roleMap = {
      leader: '工作领导人',
      contact: '驻站联络人',
      guardian: '地线监护人',
    };

    const data = await postControlTaskGetLiveVideoUrl({
      taskId: record.id,
    });

    const streams = data
      .filter((item: any) => item.arGlassId)
      .map((item: any) => ({
        key: item.personChargeRoleType + item.personChargeId + item.arGlassId,
        title: item.personChargeName + '-' + roleMap[item.personChargeRoleType],
        path: `/${record.id}/${item.arGlassId}/${item.personChargeRoleType}/${item.personChargeId}`,
      }));

    openLiveStreamModal(true, {
      streams,
      title: `实时监控画面 - ${record.taskNum}`,
      width: '90%',
      height: '700px',
    });
  }
</script>

<style scoped></style>
